## MCP Server for NocoBase

一个用于与 NocoBase 应用交互的 Model Context Protocol (MCP) 服务器，提供集合（Collections）、记录（Records）、Schema、路由（Desktop Routes）、页面区块（Blocks）等丰富的工具与资源。

### 特性概览
- 集合管理：创建、读取、更新、删除（CRUD）
- 记录操作：分页查询、过滤、排序、关联展开、CRUD
- Schema 能力：集合字段与 UI Schema 检索、页面结构分析
- 路由管理：列出、创建（页面/分组/链接）、更新、删除、移动
- 区块管理：在页面 Grid 中添加表格、表单、详情、Markdown、看板、列表、网格卡片、日历、图表等区块并删除
- **表格操作管理**：添加/删除/更新表格操作按钮、配置表格列、筛选器、排序、发送自定义请求
- **行级别操作管理**：添加表格行操作列、高级记录 CRUD、关联操作、查找或创建/更新或创建
- **看板操作管理**：看板卡片 CRUD、拖拽移动、筛选配置、分组排序字段管理、操作按钮配置
- 用户管理：列出、获取、创建、更新、删除用户，支持过滤和分页
- 角色管理：列出、获取、创建、更新、删除角色，检查当前角色，设置默认角色
- 资源暴露：将 NocoBase 的集合、Schema 与记录通过 MCP Resources 暴露给客户端

---

## 技术栈
- 运行环境：Node.js >= 18
- 语言与构建：TypeScript、tsc
- 协议 SDK：@modelcontextprotocol/sdk
- HTTP 客户端：axios
- 参数校验：zod
- 质量保障：eslint、jest

---

## 目录结构
- src/
  - index.ts：MCP 服务器入口，注册所有工具与资源
  - client.ts：NocoBase REST API 客户端封装（Collections/Records/Routes/UI Schemas）
  - tools/
    - collections.ts：集合相关工具
    - records.ts：记录相关工具
    - schema.ts：集合与页面 Schema 工具（含 analyze_page_schema 等）
    - routes.ts：桌面路由相关工具（创建/移动/删除/更新/列表）
    - blocks.ts：页面区块相关工具（表格/表单/详情/Markdown/看板/列表/网格卡片/日历/图表）
    - table-operations.ts：表格操作工具（操作按钮/列配置/筛选器/排序/自定义请求）
    - row-operations.ts：行级别操作工具（行操作列/高级CRUD/关联操作/查找或创建）
    - form-operations.ts：表单操作工具（字段管理/操作按钮/验证规则/提交配置/自定义请求/关联表单）
    - kanban-operations.ts：看板操作工具（卡片CRUD/拖拽移动/筛选配置/分组排序字段管理/操作按钮配置）
    - users.ts：用户管理工具（列表/获取/创建/更新/删除用户）
    - roles.ts：角色管理工具（列表/获取/创建/更新/删除角色，检查当前角色，设置默认角色）
  - resources/
    - index.ts：将集合、Schema、记录暴露为 MCP Resources
  - block-templates.ts / smart-operations.ts / utils.ts 等：区块模板、智能策略与工具函数
- scripts/：用于本地调试与验证的脚本（API 连接、路由/区块演示等）
- docs/：项目文档、接口说明、进展报告
- dist/：构建产物

---

## 安装与开发

- 安装依赖
```
npm install
```

- 开发模式（直接以 ts 运行）
```
npm run dev
```

- 构建与启动
```
npm run build
npm start
```

- 代码质量与测试
```
npm run lint
npm test
```

---

## 使用方法

### 命令行启动与参数
构建后会提供 CLI：mcp-server-nocobase（bin 指向 dist/index.js）。

- 命令行参数
  - --base-url <url>：NocoBase 实例 API 基地址（必填）
  - --token <token>：认证 Token（必填）
  - --app <name>：应用 App ID（必填）
  - --help：显示帮助

- 环境变量（作为参数回退）
  - NOCOBASE_BASE_URL
  - NOCOBASE_TOKEN
  - NOCOBASE_APP

示例：
```
mcp-server-nocobase \
  --base-url https://your-nocobase.com/api \
  --token xxx \
  --app your_app
```

### 与 Claude Desktop 集成
将以下配置加入：~/Library/Application Support/Claude/claude_desktop_config.json
```
{
  "mcpServers": {
    "nocobase": {
      "command": "mcp-server-nocobase",
      "args": [
        "--base-url", "https://your-nocobase-instance.com/api",
        "--token", "your-auth-token",
        "--app", "your-app-id"
      ]
    }
  }
}
```

---

## 配置说明
服务器可通过命令行参数或环境变量配置。更多背景与 API 约定可参考：
- docs/nocobase_api_intro.md
- docs/nocobase_collections_api_reference.md
- docs/nocobase_collections_api_quick_reference.md

---

## 可用工具（Tools）

### 集合管理
- list_collections(includeMeta?)：列出集合（可选元信息）
- get_collection(name)：获取集合详情
- create_collection(name, title?, description?, autoGenId?, createdAt?, updatedAt?, createdBy?, updatedBy?, fields?)
- update_collection(name, title?, description?, hidden?)
- delete_collection(name, confirm)

### 记录操作（传入 collection、id 或筛选）
- list_records(collection, page?, pageSize?, filter?, sort?, appends?)
- get_record(collection, id, appends?)
- create_record(collection, data)
- update_record(collection, id, data)
- delete_record(collection, id, confirm)

### 数据库 Schema（Collections）
- get_collection_schema(collection)：集合字段与配置
- list_fields(collection)：列出字段
- create_field(collection, name, type, interface?, description?, uiSchema?)

### UI Schema / 页面结构（前端）
- analyze_page_schema(schemaUid)：递归分析页面 UI Schema 结构（组件、装饰器、属性、ACL、数据源等）
- get_schema_properties(schemaUid)：获取指定节点的 properties（子区块）

### 路由管理（Desktop Routes）
- list_routes(tree?)：列出路由（可树形）
- routes_tree_overview(includeIds?, includeHidden?)：输出人类可读的当前路由整体结构（树形）
- get_route(id)：获取路由详情
- create_page_route(title, parentId?, icon?, template?=blank|table|dashboard, collectionName?, enableTabs?, hidden?)
- create_group_route(title, parentId?, icon?, children?)
- create_link_route(title, href, parentId?, icon?, openInNewWindow?, params?)
- update_route(id, title?, icon?, hidden?, href?, openInNewWindow?)
- delete_route(id)
- move_route(sourceId, targetId?, method?=insertAfter|prepend)

### 区块管理（Blocks，插入到 Page 的 Grid 容器中）
- get_page_schema(schemaUid)
- list_page_blocks(schemaUid)
- add_table_block(parentUid, collectionName, title?, dataSource?='main', position?)
- add_form_block(parentUid, collectionName, title?, type?=create|update, dataSource?='main', position?)
- add_details_block(parentUid, collectionName, title?, dataSource?='main', position?)
- add_markdown_block(parentUid, title?='Markdown', content?, position?)
- add_kanban_block(parentUid, collectionName, title?, groupField, sortField?, dataSource?='main', position?)
- add_list_block(parentUid, collectionName, title?, rowKey?='id', templateSchema?, dataSource?='main', position?)
- add_grid_card_block(parentUid, collectionName, title?, rowKey?='id', templateSchema?, columnCount?, dataSource?='main', position?)
- add_calendar_block(parentUid, collectionName, title?, fieldNames{title,start[,end,colorFieldName]}, showLunar?, defaultView?, enableQuickCreateEvent?, weekStart?, dataSource?='main', position?)
- add_chart_block(parentUid, collectionName, title?, chartType?, config?, dataSource?='main', position?)
- remove_block(blockUid)

> 说明：区块模板定义见 src/block-templates.ts；区块插入采用 UI Schemas 接口（insertAdjacent / insertBlockToGrid），支持 beforeBegin/afterBegin/beforeEnd/afterEnd。

### 表单操作管理（Form Operations）
#### 表单字段管理
- add_form_field(formUid, fieldName, fieldType, title?, required?, defaultValue?, validation?, componentProps?, readOnly?, hidden?, position?)：向表单添加字段
- remove_form_field(fieldUid)：移除表单字段
- configure_form_field(fieldUid, updates)：配置表单字段属性
- list_form_fields(formUid)：列出表单所有字段

#### 表单操作按钮管理
- add_form_action(formUid, actionType, title?, settings?, componentProps?, actionSettings?, position?)：添加表单操作按钮
- remove_form_action(actionUid)：移除表单操作按钮

#### 表单提交与验证配置
- configure_form_submit(actionUid, submitConfig)：配置表单提交行为（成功消息、跳转、工作流触发等）
- configure_form_validation(fieldUid, validationRules)：配置表单字段验证规则

#### 高级表单操作
- create_custom_form_request(formUid, requestConfig)：创建自定义HTTP请求操作
- configure_association_form(formUid, associationConfig)：配置关联表单

> 支持的字段类型：input, textarea, number, select, radio, checkbox, date, datetime, association, upload
> 支持的操作类型：submit, update, cancel, customRequest, bulkUpdate
> 基于 NocoBase Form Block API 映射文档实现，支持完整的表单操作流程

---

## 可用资源（Resources）
- collections://list：集合列表（JSON）
- collections://{name}：集合详情（JSON）
- collections://{name}/schema：集合 Schema（配置与字段）
- collections://{name}/records：集合记录（默认最多 50 条）
- collections://{name}/records/{id}：单条记录详情

---

## 开发进展与里程碑

### 阶段 2：API 接口扩展（已完成）
详情见：docs/STAGE2_COMPLETION_REPORT.md

核心成果：
- 扩展 NocoBaseClient：新增 UI Schema 管理方法（getPageSchema、insertBlockSchema/insertBlockToGrid、updateBlockSchema、deleteBlockSchema、getSchemaProperties、createPageSchema）
- 区块模板系统：支持表格、表单、详情、看板、Markdown 五大类模板，统一区块结构
- MCP 工具：实现 8+ 个区块管理工具（get/list/add/remove 等）
- 工具函数库：uid/deepClone/isEmpty/safeJsonParse/formatError/validateRequiredFields 等
- 测试验证：服务器与工具注册成功、集合/路由/区块操作均已通过手动与脚本验证

### 路由复杂操作测试（已通过）
详情见：docs/COMPLEX_ROUTES_TEST_REPORT.md

- 测试覆盖：CRUD、嵌套结构、移动/重排、批量操作、错误与边界、性能
- 结果摘要：共 6 组测试，全部通过；创建 24 条路由（Group 3 / Page 20 / Link 1），最深嵌套 3 层，最终全部清理
- 技术亮点：动态 ID 管理、异步请求/响应处理、自动化清理、性能基准

### 下一步计划（阶段 3：核心实现）
- 高级区块功能：看板分组字段配置、图表可视化、日历时间管理
- 区块位置管理：精确 Grid 控制、拖拽调整、响应式布局
- 区块配置系统：动态属性/权限控制/主题样式

### 相关文档
- docs/README.md：基础功能与使用说明
- docs/nocobase-block-system-analysis.md、docs/NOCOBASE_BLOCKS_ANALYSIS.md：区块系统分析
- docs/page-schema-analysis.md：页面 Schema 结构分析
- docs/smart-operations-strategy.md、docs/verification-strategy.md：智能操作与验证策略

---

## 测试与验证

### 本地脚本
scripts/ 目录下提供了大量示例与验证脚本，例如：
- test-api-connection.js / test-api-direct.js：连通性与基础调用
- test-routes.js / test-complex-routes.js：路由创建、移动与清理
- test-schema.js / test-create-page-with-blocks.js / test-new-blocks.js：Schema 与区块操作

在运行脚本前，请先 npm run build，脚本默认从 dist/ 引用客户端实现。

### 单元测试
```
npm test
```

> 注：MCP 端到端集成测试可结合 Claude / LLM 客户端进行；安全起见，请勿在公开仓库中提交真实生产凭证。

---

## 许可证与贡献
- 许可证：MIT（见 LICENSE）
- 贡献：欢迎提交 PR / Issue 改进工具能力与文档

